<?php
require_once 'config.php';

$message = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $password = $_POST['password'];

    if (empty($name) || empty($email) || empty($password)) {
        $message = "Please fill in all required fields.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = "Please enter a valid email address.";
    } else {
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        $check_sql = "SELECT id FROM users WHERE email = ?";
        $check_stmt = $conn->prepare($check_sql);

        if ($check_stmt) {
            $check_stmt->bind_param("s", $email);
            $check_stmt->execute();
            $result = $check_stmt->get_result();

            if ($result->num_rows > 0) {
                $message = "Email already exists. Please use a different email.";
            } else {
                $sql = "INSERT INTO users (name, email, phone, password, created_at) VALUES (?, ?, ?, ?, NOW())";
                $stmt = $conn->prepare($sql);

                if ($stmt) {
                    $stmt->bind_param("ssss", $name, $email, $phone, $hashed_password);

                    if ($stmt->execute()) {
                        $message = "Registration successful! User ID: " . $conn->insert_id;
                        $name = $email = $phone = "";
                    } else {
                        $message = "Error: " . $stmt->error;
                    }
                    $stmt->close();
                } else {
                    $message = "Database error: " . $conn->error;
                }
            }
            $check_stmt->close();
        } else {
            $message = "Database error: " . $conn->error;
        }
    }
}
?>

<html>
<head>
    <title>User Registration</title>
</head>
<body>
    <h1>User Registration</h1>

    <?php if ($message) echo "<p><b>$message</b></p>"; ?>

    <form method="POST" action="">
        <p>
            <label>Full Name *</label><br>
            <input type="text" name="name" value="<?php echo isset($name) ? htmlspecialchars($name) : ''; ?>" required>
        </p>

        <p>
            <label>Email Address *</label><br>
            <input type="email" name="email" value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>" required>
        </p>

        <p>
            <label>Phone Number</label><br>
            <input type="text" name="phone" value="<?php echo isset($phone) ? htmlspecialchars($phone) : ''; ?>">
        </p>

        <p>
            <label>Password *</label><br>
            <input type="password" name="password" required>
        </p>

        <p>
            <input type="submit" value="Register">
        </p>
    </form>
</body>
</html>

<?php
$conn->close();
?>
